export enum statementStatusEnum {
  '财务待确认' = '10',
  '客户待确认' = '20',
  '待提交开票' = '30',
  '待开票' = '40',
  '已开票' = '50',
  '待回款' = '60',
  '部分回款' = '70',
  '已回款' = '80',
  '作废' = '1',
}

export function getstatus(code: number): string | undefined {
  for (const key in statementStatusEnum) {
    if (isNaN(Number(key)) && statementStatusEnum[key as keyof typeof statementStatusEnum] === code) {
      return key;
    }
  }
  return undefined;
}
