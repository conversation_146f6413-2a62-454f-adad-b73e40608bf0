import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '姓名',
    dataIndex: 'realname',
    width: 150,
  },
  {
    title: '性别',
    dataIndex: 'workNo',
    width: 100,
  },
  {
    title: '客户单位',
    dataIndex: 'departName',
    width: 200,
  },
  {
    title: '年龄',
    dataIndex: 'post',
    width: 150,
    slots: { customRender: 'post' },
  },
  {
    title: '所在城市',
    width: 150,
    dataIndex: 'telephone',
  },
  {
    title: '家庭住址',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '身份证号',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '身份证有效期',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '手机号',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '学历',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '政治面貌、驾驶证类型、所在分组、驾驶证有效期、工作经验、驾驶员类型、驾驶证有效期、紧急联系人、紧急联系人电话、有无应聘应聘表、有无入职应聘表、有无员工登记表、有无路试报告、有无胜任力测评报告、有无心理测评、有无体检报告、有无无犯罪记录、有无家访、合同类型（劳动合同、劳务合同）、司机星级标准（1-5星）、入职时间、转正时间、在职状态、操作人、操作时间、操作（编辑、详情、删除）

1.基本情况：

1.1姓名：汉字校验；身份证号、手机号位数校验、

1.2校验：司机星级等级需和技能评级标准进行双向校验才可保存，出现不一致的情况需要提示。

2.紧急联系人信息

3.教育经历，点击+ 添加列表（时间范围、教育层次、毕业院校、所学专业）

4.技能相关（同步团标的数据，为根据规则设置星级标准做统计）

5.工作经历,点击+ 添加列表（入职时间、离职时间、所在单位、担任职位）

6.文件上传：9项资料（应聘登记表、入职登记表、推荐登记表、路试测评表、胜任力模型测评报告、心理测评报告、体检报告、有无犯罪记录、家访记录表）

7.其他：备注



序号

姓名

操作

编辑  详情  删除

客户单位

性别

年龄

所在城市

家庭住址

身份证号

身份证有效期

手机号

学历

政治面貌

驾驶证类型

驾驶证有效期

所在分组

工作经验

紧急联系人

紧急联系人电话

有无应聘应聘表

有无入职应聘表

有无员工登记表

有无路试报告

有无胜任力测评报告

有无心理测评

有无体检报告

有无犯罪记录

有无家访

司机星级标准

合同类型

入职时间

转正时间

在职状态

操作人

操作时间

有无纹身


有
有无纹身：无

',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '家庭住址',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '家庭住址',
    width: 150,
    dataIndex: 'email',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '姓名',
    field: 'realname',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '身份证号',
    field: 'idCard',
    component: 'Input',
    colProps: { span: 6 },
  },
];
