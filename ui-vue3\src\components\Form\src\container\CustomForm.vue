<template>
  <div :class="formDisabled ? 'custom-form-container-disabled custom-form-detail-effect' : 'custom-and-modal-form'">
    <!-- 确保 fieldset 的 disabled 属性绑定正确 -->
    <fieldset :disabled="formDisabled">
      <slot name="detail"></slot>
    </fieldset>
    <slot name="edit"></slot>
    <fieldset :disabled="formDisabled">
      <slot></slot>
    </fieldset>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, watch } from 'vue';

  export default defineComponent({
    name: 'CustomForm',
    props: {
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    setup(props, { emit }) {
      const formDisabled = ref<boolean>(props.disabled);

      watch(
        () => props.disabled,
        (value) => {
          formDisabled.value = value;
        }
      );

      return {
        formDisabled
      };
    }
  });
</script>

<style scoped lang="less">
  // 表单正常样式
  .custom-and-modal-form {
    :deep(.ant-form-item-label) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-right: 6px;
      > label {
        line-height: 32px;
        display: inline;
        &::after {
          margin-right: 0;
        }
      }
    }
  }

  // 禁用状态容器样式
  .custom-form-container-disabled {
    cursor: not-allowed;
  }

  .custom-form-container-disabled fieldset[disabled] {
    -ms-pointer-events: none;
    pointer-events: none;
  }

  .custom-form-container-disabled :deep(.ant-select) {
    -ms-pointer-events: none;
    pointer-events: none;
  }

  // 亮色主题下禁用样式
  html[data-theme='light'] {
    .custom-form-detail-effect {
      :deep(.ant-select-selector),
      :deep(.ant-btn),
      :deep(.ant-input),
      :deep(.ant-input-affix-wrapper),
      :deep(.ant-picker),
      :deep(.ant-input-number) {
        color: #606266 !important;
      }
      :deep(.ant-select) {
        color: #606266 !important;
      }
      :deep(.ant-select-selection-item-content),
      :deep(.ant-select-selection-item),
      :deep(input) {
        color: #606266 !important;
      }

      :deep(.ant-radio-wrapper),
      :deep(.ant-checkbox-wrapper),
      :deep(.ant-btn) {
        color: rgba(0, 0, 0, 0.65);
      }
      :deep(.ant-radio-wrapper .ant-radio-inner:after),
      :deep(.ant-checkbox-checked .ant-checkbox-inner) {
        color: #606266 !important;
      }
      :deep(.ant-radio-inner),
      :deep(.ant-checkbox-inner) {
        border-color: rgba(51, 51, 51, 0.25) !important;
        background-color: rgba(51, 51, 51, 0.04) !important;
      }
      :deep(.ant-checkbox-checked .ant-checkbox-inner::after),
      :deep(.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after) {
        border-color: rgba(51, 51, 51, 0.25) !important;
      }
      :deep(.ant-switch) {
        background-color: rgba(51, 51, 51, 0.25);
      }
      :deep(.tox .tox-toolbar__group),
      :deep(.tox .tox-edit-area__iframe),
      :deep(.vditor-toolbar),
      :deep(.vditor-preview),
      :deep(.custom-tinymce-img-upload) {
        background: rgba(51, 51, 51, 0.04);
      }
    }
  }

  // 暗色主题下禁用样式
  html[data-theme='dark'] {
    .custom-form-detail-effect {
      :deep(.ant-select-selector),
      :deep(.ant-btn),
      :deep(.ant-input),
      :deep(.ant-input-affix-wrapper),
      :deep(.ant-picker),
      :deep(.ant-input-number) {
        color: rgba(255, 255, 255, 0.25) !important;
      }
      :deep(.ant-select) {
        color: rgba(255, 255, 255, 0.25) !important;
      }
      :deep(.ant-select-selection-item-content),
      :deep(.ant-select-selection-item),
      :deep(input) {
        color: rgba(255, 255, 255, 0.25) !important;
      }

      :deep(.ant-radio-wrapper),
      :deep(.ant-checkbox-wrapper) {
        color: rgba(255, 255, 255, 0.25);
      }
      :deep(.ant-radio-wrapper .ant-radio-inner:after),
      :deep(.ant-checkbox-checked .ant-checkbox-inner) {
        background-color: rgba(255, 255, 255, 0.08);
      }
      :deep(.ant-radio-inner),
      :deep(.ant-checkbox-inner) {
        border-color: #424242 !important;
        background-color: rgba(255, 255, 255, 0.08);
      }
      :deep(.ant-switch) {
        background-color: rgba(51, 51, 51, 0.25);
        opacity: 0.65;
      }
      :deep(.tox .tox-toolbar__group),
      :deep(.tox .tox-edit-area__iframe),
      :deep(.vditor-toolbar),
      :deep(.vditor-preview),
      :deep(.custom-tinymce-img-upload) {
        background: rgba(51, 51, 51, 0.04);
      }
    }
  }

  // 上传组件相关样式
  .custom-form-container-disabled :deep(.ant-upload-select) {
    cursor: grabbing;
  }

  .custom-form-container-disabled :deep(.ant-upload-list) {
    cursor: grabbing;
  }

  .custom-form-container-disabled fieldset[disabled] :deep(.ant-upload-list) {
    -ms-pointer-events: auto !important;
    pointer-events: auto !important;
  }

  .custom-form-container-disabled fieldset[disabled] iframe {
    -ms-pointer-events: auto !important;
    pointer-events: auto !important;
  }

  .custom-form-container-disabled :deep(.ant-upload-list-item-actions .anticon-delete),
  .custom-form-container-disabled :deep(.ant-upload-list-item .anticon-close) {
    display: none;
  }

  .custom-form-container-disabled :deep(.vditor-sv) {
    display: none !important;
    background: rgba(51, 51, 51, 0.04);
  }
</style>
