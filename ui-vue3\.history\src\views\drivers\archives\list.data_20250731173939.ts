import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '姓名',
    dataIndex: 'realname',
    width: 150,
  },
  {
    title: '性别',
    dataIndex: 'workNo',
    width: 100,
  },
  {
    title: '客户单位',
    dataIndex: 'departName',
    width: 200,
  },
  {
    title: '年龄',
    dataIndex: 'post',
    width: 150,
    slots: { customRender: 'post' },
  },
  {
    title: '所在城市',
    width: 150,
    dataIndex: 'telephone',
  },
  {
    title: '家庭住址',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '身份证号',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '身份证有效期',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '手机号',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '学历',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '政治面貌',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '驾驶证类型',
    width: 150,
    dataIndex: 'email',
  },
  {
    title: '所在分组',
    width: 150,
    dataIndex: 'email',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '姓名',
    field: 'realname',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '身份证号',
    field: 'idCard',
    component: 'Input',
    colProps: { span: 6 },
  },
];
