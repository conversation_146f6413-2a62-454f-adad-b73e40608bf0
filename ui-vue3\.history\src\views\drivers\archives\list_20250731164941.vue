<template>
  <div class="driver-archives-container p-2">
    <!-- 左侧分组树 -->
    <div class="left-pane">
      <div class="group-header">
        <div class="header-content">
          <FolderOpenOutlined class="header-icon" />
          <span class="header-title">驾驶员分组</span>
          <div class="header-actions">
            <a-tooltip title="添加分组">
              <PlusOutlined class="action-btn add-btn" @click="handleAddGroup" />
            </a-tooltip>
            <a-tooltip title="刷新">
              <ReloadOutlined class="action-btn refresh-btn" @click="handleRefreshTree" />
            </a-tooltip>
          </div>
        </div>
      </div>
      <div class="tree-container">
        <BasicTree
          ref="treeRef"
          :treeData="treeData"
          :actionList="actionList"
          :selectedKeys="selectedKeys"
          :expandedKeys="expandedKeys"
          :renderIcon="renderIcon"
          :fieldNames="{ children: 'children', title: 'title', key: 'key' }"
          :defaultExpandAll="true"
          :highlight="true"
          toolbar
          search
          @select="handleTreeSelect"
          @expand="handleTreeExpand"
          @search="handleSearch"
        />
      </div>
    </div>
    <!-- 右侧列表 -->
    <div class="right-pane ml-2">
      <BasicTable @register="registerTable">
        <template #tableTitle>
          <a-button type="primary" v-auth="'oc:hr_driver_report:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        </template>
        <!--操作栏-->
        <template #action="{ record }">
          <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
        </template>
      </BasicTable>
    </div>
  </div>

  <!-- 分组弹窗 -->
  <a-modal
    v-model:visible="groupModalVisible"
    :title="groupModalTitle"
    :bodyStyle="{ maxHeight: '700px', overflowY: 'auto' }"
    @ok="handleGroupModalOk"
    @cancel="handleGroupModalCancel"
  >
    <a-form ref="groupFormRef" :model="groupForm" :rules="groupFormRules" layout="vertical">
      <a-form-item label="分组" name="name">
        <a-input v-model:value="groupForm.name" placeholder="请输入分组名称" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, h } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { PlusOutlined, ReloadOutlined, FolderOpenOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { BasicTree, ActionItem } from '/@/components/Tree';
  import { columns, searchFormSchema } from './list.data';
  import { BasicTable,TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { list } from './list.api';
  // 响应式数据
  const treeRef = ref();
  const groupFormRef = ref();
  const selectedKeys = ref(['all']);
  const expandedKeys = ref(['all']);
  const treeData = ref<any[]>([]);

  // 树节点操作按钮配置
  const actionList: ActionItem[] = [
    {
      render: (node) => {
        return h(EditOutlined, {
          class: 'ml-2 tree-action-btn edit-btn',
          onClick: (e: Event) => {
            e.stopPropagation();
            handleEditGroup(node.key);
          },
        });
      },
      show: (node) => node.key !== 'all', // 根节点不显示编辑按钮
    },
    {
      render: (node) => {
        return h(DeleteOutlined, {
          class: 'ml-2 tree-action-btn delete-btn',
          onClick: (e: Event) => {
            e.stopPropagation();
            handleDeleteGroup(node.key);
          },
        });
      },
      show: (node) => node.key !== 'all', // 根节点不显示删除按钮
    },
  ];
  const renderIcon = (node: any) => {
    if (node.key === 'all') {
      return 'ant-design:folder-open-outlined';
    } else {
      return 'ant-design:folder-outlined';
    }
  };
  // 分组弹窗相关
  const groupModalVisible = ref(false);
  const groupModalTitle = ref('');
  const isEditGroup = ref(false);
  const currentGroupKey = ref('');
  const groupForm = reactive({
    name: '',
  });

  // 表单验证规则
  const groupFormRules = {
    name: [{ required: true, message: '请输入车队名称', trigger: 'blur' }],
  };

  // Mock数据
  const mockGroupTree = [
    {
      key: 'group1',
      title: '车队1',
      driverCount: 10,
      description: '第一个车队',
      children: [],
      isLeaf: true,
    },
    {
      key: 'group2',
      title: '车队2',
      driverCount: 10,
      description: '第二个车队',
      children: [],
      isLeaf: true,
    },
  ];
  //查询区域
  const queryParam = reactive({
    name: '',
  });
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });
  const searchQuery = () => {
    console.log('查询参数:', queryParam);
  };
  // 初始化数据
  onMounted(() => {
    loadTreeData();
  });

  // 加载分组树数据
  const loadTreeData = () => {
    const allGroup = {
      key: 'all',
      title: '所有分组',
      children: mockGroupTree || [],
      isLeaf: false,
    };
    treeData.value = [allGroup];
    expandedKeys.value = ['all'];
  };

  // 树节点选择事件
  const handleTreeSelect = (keys: string[]) => {
    if (keys.length > 0) {
      selectedKeys.value = keys;
    }
  };

  // 树节点展开事件
  const handleTreeExpand = (keys: string[]) => {
    expandedKeys.value = keys;
  };

  // 搜索事件 - BasicTree内置搜索功能
  const handleSearch = (searchValue: string) => {
    console.log('搜索:', searchValue);
  };

  // 刷新树
  const handleRefreshTree = () => {
    loadTreeData();
    message.success('刷新成功');
  };

  // 添加分组
  const handleAddGroup = () => {
    groupModalTitle.value = '添加分组';
    isEditGroup.value = false;
    currentGroupKey.value = '';
    groupForm.name = '';
    groupModalVisible.value = true;
  };

  // 编辑分组
  const handleEditGroup = (key: string) => {
    // 查找要编辑的分组数据
    const findGroup = (nodes: any[], targetKey: string): any => {
      for (const node of nodes) {
        if (node.key === targetKey) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const found = findGroup(node.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    const group = findGroup(treeData.value, key);
    if (group) {
      groupModalTitle.value = '分组';
      isEditGroup.value = true;
      currentGroupKey.value = key;
      groupForm.name = group.title;
      groupModalVisible.value = true;
    }
  };

  // 删除分组
  const handleDeleteGroup = (key: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确认删除该车队？删除后该分组下的驾驶员将移至"所有分组"',
      onOk() {
        // 模拟删除操作
        console.log('删除分组:', key);
        message.success('删除成功');
        loadTreeData();
        if (selectedKeys.value.includes(key)) {
          selectedKeys.value = ['all'];
        }
      },
    });
  };

  // 分组弹窗确认
  const handleGroupModalOk = async () => {
    try {
      await groupFormRef.value?.validate();
      // 模拟保存操作
      console.log('保存分组:', {
        name: groupForm.name,
        isEdit: isEditGroup.value,
        id: currentGroupKey.value,
      });
      message.success(isEditGroup.value ? '编辑成功' : '添加成功');
      groupModalVisible.value = false;
      loadTreeData();
    } catch (error) {
      console.error('保存分组失败:', error);
    }
  };

  // 分组弹窗取消
  const handleGroupModalCancel = () => {
    groupModalVisible.value = false;
  };
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      api: list,
      columns,
      //update-begin---author:wangshuai ---date:20220629  for：[VUEN-1485]进入系统管理--通讯录页面后，网页命令行报错------------
      //update-end---author:wangshuai ---date:20220629  for：[VUEN-1485]进入系统管理--通讯录页面后，网页命令行报错--------------
      showIndexColumn: true,
      formConfig: {
        schemas: searchFormSchema,
      },
      canResize: false,
      showActionColumn: true,
      actionColumn: {
        width: 120,
      },
      showTableSetting: false,
      pagination: true,
    },
  });
  const getTableAction = (record) => {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'drivers:archives:edit',
      },
    ];
  };
  const getDropDownAction = (record) => {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'drivers:archives:delete',
      },
    ];
  };
  const handleEdit = (record) => {
    console.log('编辑:', record);
  };
  const handleDetail = (record) => {
    console.log('详情:', record);
  };
  const handleDelete = (record) => {
    console.log('删除:', record);
  };
  //注册table数据
  const [registerTable, { reload }] = tableContext;
</script>
<style lang="less" scoped>
  .driver-archives-container {
    display: flex;
    height: calc(100vh - 120px);
    background: #f5f5f5;
  }

  // 左侧面板
  .left-pane {
    width: 300px;
    min-width: 280px;
    max-width: 400px;
    background: #fff;
    border-right: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
  }

  // 右侧面板
  .right-pane {
    flex: 1;
    background: #fff;
  }

  // 通用头部样式
  .group-header,
  .list-header {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;

    .header-content {
      display: flex;
      align-items: center;
      color: #fff;

      .header-icon {
        font-size: 18px;
        margin-right: 8px;
      }

      .header-title {
        font-size: 16px;
        font-weight: 500;
        flex: 1;
      }

      .header-actions {
        display: flex;
        gap: 8px;

        .action-btn {
          padding: 6px;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s;
          background: rgba(255, 255, 255, 0.1);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
          }

          &.add-btn:hover {
            color: #52c41a;
          }

          &.refresh-btn:hover {
            color: #1890ff;
          }
        }
      }
    }
  }

  // 树容器
  .tree-container {
    flex: 1;
    padding: 8px;
    overflow: auto;
  }

  // 列表容器
  .list-container {
    flex: 1;
    padding: 16px;
    overflow: auto;
  }

  // 列表占位符
  .list-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .placeholder-content {
      text-align: center;
      color: #999;

      .placeholder-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      .placeholder-text {
        font-size: 14px;
      }
    }
  }

  // 树节点样式
  :deep(.vben-basic-tree) {
    height: 100%;

    .vben-tree {
      background: transparent;

      .ant-tree-node-content-wrapper {
        border-radius: 4px;
        transition: all 0.2s;
        margin: 2px 0;

        &:hover {
          background: #e6f7ff;
        }

        &.ant-tree-node-selected {
          background: #bae7ff;
          border: 1px solid #91d5ff;
        }
      }

      .tree-node-title {
        display: flex;
        align-items: center;
        gap: 6px;
        width: 100%;
        padding: 4px 8px;

        .node-icon {
          color: #1890ff;
          font-size: 14px;
        }

        .node-text {
          flex: 1;
          font-size: 14px;
          color: #333;
        }

        .driver-count {
          color: #999;
          font-size: 12px;
          background: #f0f0f0;
          padding: 2px 6px;
          border-radius: 10px;
        }

        .tree-node-actions {
          display: flex;
          gap: 4px;
          margin-left: 8px;
          opacity: 0;
          transition: opacity 0.2s;

          .tree-action-btn {
            padding: 2px 4px;
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 12px;

            &:hover {
              background: rgba(24, 144, 255, 0.1);
            }

            &.edit-btn {
              color: #1890ff;

              &:hover {
                color: #40a9ff;
                background: rgba(24, 144, 255, 0.1);
              }
            }

            &.delete-btn {
              color: #ff4d4f;

              &:hover {
                color: #ff7875;
                background: rgba(255, 77, 79, 0.1);
              }
            }
          }
        }
      }

      .ant-tree-node-content-wrapper:hover .tree-node-actions {
        opacity: 1;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .driver-archives-container {
      flex-direction: column;
      height: auto;
    }

    .left-pane {
      width: 100%;
      max-width: none;
      min-width: auto;
      border-right: none;
      border-bottom: 1px solid #e8e8e8;
    }

    .divider {
      display: none;
    }

    .right-pane {
      min-height: 400px;
    }
  }
</style>
